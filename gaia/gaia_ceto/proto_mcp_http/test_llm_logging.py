#!/usr/bin/env python3
"""
Test script to trigger LLM calls and see latency logging in the server output.
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from mcp_http_clientlib import MCPClientLib

async def test_llm_logging():
    """Test LLM latency logging by calling the test_llm_call tool."""
    
    # Initialize client
    client = MCPClientLib()
    
    try:
        # Connect to the server
        print("Connecting to MCP server...")
        success = await client.connect_to_server("http://localhost:9000/mcp")
        if not success:
            print("❌ Failed to connect to server")
            return
        
        print("✅ Connected to server")
        
        # Call the test_llm_call tool to trigger LLM latency logging
        print("\n🧪 Testing LLM call with latency logging...")
        result = await client.call_tool(
            tool_name="test_llm_call",
            tool_input={"query": "What is the capital of France?"},
            tool_call_id="test_call_1"
        )
        
        print(f"📊 Tool call result:")
        print(f"  Success: {result.success}")
        if result.success:
            print(f"  Content: {result.content}")
        else:
            print(f"  Error: {result.error}")
        
        # Also test the process_query method directly
        print("\n🧪 Testing direct process_query...")
        query_result = await client.process_query(
            query="What is 5 + 7?",
            model="claude-3-5-sonnet-20241022",
            max_tokens=100
        )
        
        print(f"📊 Query result:")
        if query_result.get('error'):
            print(f"  Error: {query_result['error']}")
        else:
            print(f"  Response: {query_result.get('final_text', 'No response')}")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        await client.cleanup()
        print("🧹 Cleanup complete")

if __name__ == "__main__":
    print("🚀 Starting LLM latency logging test...")
    asyncio.run(test_llm_logging())
